use std::{
    ffi::OsStr,
    fs,
    path::{Path, PathBuf},
    process,
};

fn main() {
    println!("🔧 Starting protobuf code generation...");

    let generated_dir = "src/generated";
    let protos_dir = "protos";

    if !Path::new(protos_dir).exists() {
        eprintln!("❌ Error: Protos directory not found: {}", protos_dir);
        process::exit(1);
    }

    let proto_files: Vec<PathBuf> = match fs::read_dir(protos_dir) {
        Ok(entries) => entries
            .filter_map(|entry| {
                let path = entry.ok()?.path();
                if path.extension() == Some(OsStr::new("proto")) { Some(path) } else { None }
            })
            .collect(),
        Err(e) => {
            eprintln!("❌ Error: Failed to read protos directory: {}", e);
            process::exit(1);
        }
    };

    if proto_files.is_empty() {
        eprintln!("❌ Error: No .proto files found in {} directory", protos_dir);
        process::exit(1);
    }

    println!("📋 Found {} proto file(s):", proto_files.len());
    for proto_file in &proto_files {
        if let Some(file_name) = proto_file.file_name() {
            println!("   - {}", file_name.to_string_lossy());
        }
    }

    if Path::new(generated_dir).exists() {
        println!("🧹 Cleaning existing generated code...");
        if let Err(e) = fs::remove_dir_all(generated_dir) {
            eprintln!("❌ Error: Failed to clean generated directory: {}", e);
            process::exit(1);
        }
    }

    println!("📁 Creating generated directory...");
    if let Err(e) = fs::create_dir_all(generated_dir) {
        eprintln!("❌ Error: Failed to create generated directory: {}", e);
        process::exit(1);
    }

    println!("⚙️  Compiling protobuf definitions...");
    let result =
        tonic_build::configure().out_dir(generated_dir).build_server(false).compile_protos(&proto_files, &[protos_dir]);

    match result {
        Ok(_) => {
            println!("✅ Protobuf code generation completed successfully!");
            println!("📂 Generated files location: {}", generated_dir);
            println!("💡 Import with: use crate::generated::<module_name>::*;");
            println!("🎉 All done! Generated code is ready to use.");
        }
        Err(e) => {
            eprintln!("❌ Error: Failed to compile protobuf: {}", e);
            process::exit(1);
        }
    }
}
