{"rustc": 8210029788606052455, "features": "[\"default\", \"prost\", \"prost-build\", \"transport\"]", "declared_features": "[\"cleanup-markdown\", \"default\", \"prost\", \"prost-build\", \"transport\"]", "target": 9025750215440372010, "profile": 5371775047336160619, "path": 7934114986830911633, "deps": [[2739579679802620019, "prost_build", false, 1983988644765210570], [3060637413840920116, "proc_macro2", false, 12566304975108045480], [8549471757621926118, "prettyplease", false, 15073598640409775832], [16470553738848018267, "prost_types", false, 2751395780730433441], [17990358020177143287, "quote", false, 10986496616990155107], [18149961000318489080, "syn", false, 12754508840024337264]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tonic-build-b97a58111ba77c68/dep-lib-tonic_build", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}