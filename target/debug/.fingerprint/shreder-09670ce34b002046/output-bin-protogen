{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"protogen.rs","byte_start":273,"byte_end":339,"line_start":15,"line_end":15,"column_start":9,"column_end":73,"is_primary":true,"text":[{"text":"        eprintln!(\"❌ Error: Protos directory not found: {}\", protos_dir);","highlight_start":9,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::uninlined_format_args)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"protogen.rs","byte_start":324,"byte_end":324,"line_start":15,"line_end":15,"column_start":58,"column_end":58,"is_primary":true,"text":[{"text":"        eprintln!(\"❌ Error: Protos directory not found: {}\", protos_dir);","highlight_start":58,"highlight_end":58}],"label":null,"suggested_replacement":"protos_dir","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"protogen.rs","byte_start":326,"byte_end":338,"line_start":15,"line_end":15,"column_start":60,"column_end":72,"is_primary":true,"text":[{"text":"        eprintln!(\"❌ Error: Protos directory not found: {}\", protos_dir);","highlight_start":60,"highlight_end":72}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mprotogen.rs:15:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        eprintln!(\"❌ Error: Protos directory not found: {}\", protos_dir);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(clippy::uninlined_format_args)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        eprintln!(\"❌ Error: Protos directory not found: {}\",\u001b[0m\u001b[0m\u001b[38;5;9m protos_dir)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        eprintln!(\"❌ Error: Protos directory not found: {\u001b[0m\u001b[0m\u001b[38;5;10mprotos_dir\u001b[0m\u001b[0m}\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"protogen.rs","byte_start":720,"byte_end":782,"line_start":27,"line_end":27,"column_start":13,"column_end":73,"is_primary":true,"text":[{"text":"            eprintln!(\"❌ Error: Failed to read protos directory: {}\", e);","highlight_start":13,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"protogen.rs","byte_start":776,"byte_end":776,"line_start":27,"line_end":27,"column_start":67,"column_end":67,"is_primary":true,"text":[{"text":"            eprintln!(\"❌ Error: Failed to read protos directory: {}\", e);","highlight_start":67,"highlight_end":67}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"protogen.rs","byte_start":778,"byte_end":781,"line_start":27,"line_end":27,"column_start":69,"column_end":72,"is_primary":true,"text":[{"text":"            eprintln!(\"❌ Error: Failed to read protos directory: {}\", e);","highlight_start":69,"highlight_end":72}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mprotogen.rs:27:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            eprintln!(\"❌ Error: Failed to read protos directory: {}\", e);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            eprintln!(\"❌ Error: Failed to read protos directory: {}\",\u001b[0m\u001b[0m\u001b[38;5;9m e)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            eprintln!(\"❌ Error: Failed to read protos directory: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"protogen.rs","byte_start":872,"byte_end":945,"line_start":33,"line_end":33,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        eprintln!(\"❌ Error: No .proto files found in {} directory\", protos_dir);","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"protogen.rs","byte_start":920,"byte_end":920,"line_start":33,"line_end":33,"column_start":55,"column_end":55,"is_primary":true,"text":[{"text":"        eprintln!(\"❌ Error: No .proto files found in {} directory\", protos_dir);","highlight_start":55,"highlight_end":55}],"label":null,"suggested_replacement":"protos_dir","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"protogen.rs","byte_start":932,"byte_end":944,"line_start":33,"line_end":33,"column_start":67,"column_end":79,"is_primary":true,"text":[{"text":"        eprintln!(\"❌ Error: No .proto files found in {} directory\", protos_dir);","highlight_start":67,"highlight_end":79}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mprotogen.rs:33:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        eprintln!(\"❌ Error: No .proto files found in {} directory\", protos_dir);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        eprintln!(\"❌ Error: No .proto files found in {} directory\",\u001b[0m\u001b[0m\u001b[38;5;9m protos_dir)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        eprintln!(\"❌ Error: No .proto files found in {\u001b[0m\u001b[0m\u001b[38;5;10mprotos_dir\u001b[0m\u001b[0m} directory\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"protogen.rs","byte_start":1396,"byte_end":1462,"line_start":47,"line_end":47,"column_start":13,"column_end":77,"is_primary":true,"text":[{"text":"            eprintln!(\"❌ Error: Failed to clean generated directory: {}\", e);","highlight_start":13,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"protogen.rs","byte_start":1456,"byte_end":1456,"line_start":47,"line_end":47,"column_start":71,"column_end":71,"is_primary":true,"text":[{"text":"            eprintln!(\"❌ Error: Failed to clean generated directory: {}\", e);","highlight_start":71,"highlight_end":71}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"protogen.rs","byte_start":1458,"byte_end":1461,"line_start":47,"line_end":47,"column_start":73,"column_end":76,"is_primary":true,"text":[{"text":"            eprintln!(\"❌ Error: Failed to clean generated directory: {}\", e);","highlight_start":73,"highlight_end":76}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mprotogen.rs:47:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m47\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            eprintln!(\"❌ Error: Failed to clean generated directory: {}\", e);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m47\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            eprintln!(\"❌ Error: Failed to clean generated directory: {}\",\u001b[0m\u001b[0m\u001b[38;5;9m e)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m47\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            eprintln!(\"❌ Error: Failed to clean generated directory: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"protogen.rs","byte_start":1629,"byte_end":1696,"line_start":54,"line_end":54,"column_start":9,"column_end":74,"is_primary":true,"text":[{"text":"        eprintln!(\"❌ Error: Failed to create generated directory: {}\", e);","highlight_start":9,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"protogen.rs","byte_start":1690,"byte_end":1690,"line_start":54,"line_end":54,"column_start":68,"column_end":68,"is_primary":true,"text":[{"text":"        eprintln!(\"❌ Error: Failed to create generated directory: {}\", e);","highlight_start":68,"highlight_end":68}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"protogen.rs","byte_start":1692,"byte_end":1695,"line_start":54,"line_end":54,"column_start":70,"column_end":73,"is_primary":true,"text":[{"text":"        eprintln!(\"❌ Error: Failed to create generated directory: {}\", e);","highlight_start":70,"highlight_end":73}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mprotogen.rs:54:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        eprintln!(\"❌ Error: Failed to create generated directory: {}\", e);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        eprintln!(\"❌ Error: Failed to create generated directory: {}\",\u001b[0m\u001b[0m\u001b[38;5;9m e)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        eprintln!(\"❌ Error: Failed to create generated directory: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"protogen.rs","byte_start":2057,"byte_end":2117,"line_start":65,"line_end":65,"column_start":13,"column_end":70,"is_primary":true,"text":[{"text":"            println!(\"📂 Generated files location: {}\", generated_dir);","highlight_start":13,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"protogen.rs","byte_start":2099,"byte_end":2099,"line_start":65,"line_end":65,"column_start":52,"column_end":52,"is_primary":true,"text":[{"text":"            println!(\"📂 Generated files location: {}\", generated_dir);","highlight_start":52,"highlight_end":52}],"label":null,"suggested_replacement":"generated_dir","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"protogen.rs","byte_start":2101,"byte_end":2116,"line_start":65,"line_end":65,"column_start":54,"column_end":69,"is_primary":true,"text":[{"text":"            println!(\"📂 Generated files location: {}\", generated_dir);","highlight_start":54,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mprotogen.rs:65:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            println!(\"📂 Generated files location: {}\", generated_dir);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            println!(\"📂 Generated files location: {}\",\u001b[0m\u001b[0m\u001b[38;5;9m generated_dir)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            println!(\"📂 Generated files location: {\u001b[0m\u001b[0m\u001b[38;5;10mgenerated_dir\u001b[0m\u001b[0m}\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"protogen.rs","byte_start":2316,"byte_end":2373,"line_start":70,"line_end":70,"column_start":13,"column_end":68,"is_primary":true,"text":[{"text":"            eprintln!(\"❌ Error: Failed to compile protobuf: {}\", e);","highlight_start":13,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"protogen.rs","byte_start":2367,"byte_end":2367,"line_start":70,"line_end":70,"column_start":62,"column_end":62,"is_primary":true,"text":[{"text":"            eprintln!(\"❌ Error: Failed to compile protobuf: {}\", e);","highlight_start":62,"highlight_end":62}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"protogen.rs","byte_start":2369,"byte_end":2372,"line_start":70,"line_end":70,"column_start":64,"column_end":67,"is_primary":true,"text":[{"text":"            eprintln!(\"❌ Error: Failed to compile protobuf: {}\", e);","highlight_start":64,"highlight_end":67}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mprotogen.rs:70:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m70\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            eprintln!(\"❌ Error: Failed to compile protobuf: {}\", e);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m70\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            eprintln!(\"❌ Error: Failed to compile protobuf: {}\",\u001b[0m\u001b[0m\u001b[38;5;9m e)\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m70\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            eprintln!(\"❌ Error: Failed to compile protobuf: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"7 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 7 warnings emitted\u001b[0m\n\n"}
