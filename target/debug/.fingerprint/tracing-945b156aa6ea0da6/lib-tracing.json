{"rustc": 8210029788606052455, "features": "[\"attributes\", \"default\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 3992724396554112236, "path": 12040546271224300493, "deps": [[1906322745568073236, "pin_project_lite", false, 4770352351195433967], [3424551429995674438, "tracing_core", false, 6491614999235935019], [15574202673389706213, "tracing_attributes", false, 5981624331706043402]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-945b156aa6ea0da6/dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}