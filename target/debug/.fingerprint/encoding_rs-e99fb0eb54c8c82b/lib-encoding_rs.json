{"rustc": 8210029788606052455, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 8276155916380437441, "path": 17640807469601094085, "deps": [[10411997081178400487, "cfg_if", false, 5921440588392524561]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/encoding_rs-e99fb0eb54c8c82b/dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}