{"rustc": 8210029788606052455, "features": "[]", "declared_features": "[\"__private\", \"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 9791267232460094210, "path": 17301405828534346599, "deps": [[40386456601120721, "percent_encoding", false, 16200396926407406596], [784494742817713399, "tower_service", false, 11185230098109331927], [1906322745568073236, "pin_project_lite", false, 17404025490255456001], [2517136641825875337, "sync_wrapper", false, 13254419065701969197], [3129130049864710036, "memchr", false, 1121949639550336623], [5695049318159433696, "tower", false, 14662355957467144457], [7695812897323945497, "itoa", false, 8139172430986467801], [7712452662827335977, "tower_layer", false, 10528463933853448539], [7858942147296547339, "rustversion", false, 4677344163514874212], [8913795983780778928, "matchit", false, 1350960499808772381], [9010263965687315507, "http", false, 11365197314612370665], [9689903380558560274, "serde", false, 6055398166522160619], [10229185211513642314, "mime", false, 1831068390321789054], [10629569228670356391, "futures_util", false, 10731571793185751074], [14084095096285906100, "http_body", false, 5145177698236886216], [15176407853393882315, "axum_core", false, 10105108054621358941], [16066129441945555748, "bytes", false, 11841174785958436283], [16900715236047033623, "http_body_util", false, 13494700606986708818]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-09c1b2ab4edf2c60/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}