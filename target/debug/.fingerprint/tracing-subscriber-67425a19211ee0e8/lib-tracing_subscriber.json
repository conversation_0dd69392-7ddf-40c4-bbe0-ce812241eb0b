{"rustc": 8210029788606052455, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 3992724396554112236, "path": 17770270771451006161, "deps": [[1009387600818341822, "matchers", false, 9951671228511163492], [1017461770342116999, "sharded_slab", false, 10360790326253821689], [3424551429995674438, "tracing_core", false, 6491614999235935019], [3666196340704888985, "smallvec", false, 18191641647451605924], [3722963349756955755, "once_cell", false, 639644047255681322], [6981130804689348050, "tracing_serde", false, 16975177620552003926], [8606274917505247608, "tracing", false, 10182547110720419012], [8614575489689151157, "nu_ansi_term", false, 7043225064511154231], [9451456094439810778, "regex", false, 3118517452537995341], [9689903380558560274, "serde", false, 8947787497587030227], [10806489435541507125, "tracing_log", false, 18015792550287413378], [12427285511609802057, "thread_local", false, 18057124310051857282], [15367738274754116744, "serde_json", false, 3579932983937338233]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-67425a19211ee0e8/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}