{"rustc": 8210029788606052455, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 336243669335521001, "path": 17099334823361099506, "deps": [[1615478164327904835, "pin_utils", false, 14374510154559594923], [1906322745568073236, "pin_project_lite", false, 4770352351195433967], [6955678925937229351, "slab", false, 2323394304180403018], [7620660491849607393, "futures_core", false, 7936180777004840429], [10565019901765856648, "futures_macro", false, 6283858830572701750], [16240732885093539806, "futures_task", false, 13542304990688579067]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-bba09c76c93d57a2/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}