{"$message_type":"diagnostic","message":"unused imports: `SubscribeRequestFilterAccounts`, `SubscribeRequestFilterSlots`, and `SubscribeRequestFilterTransactions`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/common/client/connection.rs","byte_start":318,"byte_end":348,"line_start":14,"line_end":14,"column_start":37,"column_end":67,"is_primary":true,"text":[{"text":"    Entry, SubscribeEntriesRequest, SubscribeRequestFilterAccounts, SubscribeRequestFilterSlots,","highlight_start":37,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/common/client/connection.rs","byte_start":350,"byte_end":377,"line_start":14,"line_end":14,"column_start":69,"column_end":96,"is_primary":true,"text":[{"text":"    Entry, SubscribeEntriesRequest, SubscribeRequestFilterAccounts, SubscribeRequestFilterSlots,","highlight_start":69,"highlight_end":96}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/common/client/connection.rs","byte_start":383,"byte_end":417,"line_start":15,"line_end":15,"column_start":5,"column_end":39,"is_primary":true,"text":[{"text":"    SubscribeRequestFilterTransactions, shredstream_proxy_client::ShredstreamProxyClient,","highlight_start":5,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/common/client/connection.rs","byte_start":316,"byte_end":417,"line_start":14,"line_end":15,"column_start":35,"column_end":39,"is_primary":true,"text":[{"text":"    Entry, SubscribeEntriesRequest, SubscribeRequestFilterAccounts, SubscribeRequestFilterSlots,","highlight_start":35,"highlight_end":97},{"text":"    SubscribeRequestFilterTransactions, shredstream_proxy_client::ShredstreamProxyClient,","highlight_start":1,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `SubscribeRequestFilterAccounts`, `SubscribeRequestFilterSlots`, and `SubscribeRequestFilterTransactions`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/connection.rs:14:37\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Entry, SubscribeEntriesRequest, SubscribeRequestFilterAccounts, SubscribeRequestFilterSlots,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    SubscribeRequestFilterTransactions, shredstream_proxy_client::ShredstreamProxyClient,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `BackoffConfig`, `CircuitBreakerConfig`, and `DisconnectCallback`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/common/client/mod.rs","byte_start":107,"byte_end":120,"line_start":7,"line_end":7,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"pub use config::{BackoffConfig, CircuitBreakerConfig, DisconnectCallback, FilterConfig, GrpcClientConfig};","highlight_start":18,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/common/client/mod.rs","byte_start":122,"byte_end":142,"line_start":7,"line_end":7,"column_start":33,"column_end":53,"is_primary":true,"text":[{"text":"pub use config::{BackoffConfig, CircuitBreakerConfig, DisconnectCallback, FilterConfig, GrpcClientConfig};","highlight_start":33,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/common/client/mod.rs","byte_start":144,"byte_end":162,"line_start":7,"line_end":7,"column_start":55,"column_end":73,"is_primary":true,"text":[{"text":"pub use config::{BackoffConfig, CircuitBreakerConfig, DisconnectCallback, FilterConfig, GrpcClientConfig};","highlight_start":55,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/common/client/mod.rs","byte_start":107,"byte_end":164,"line_start":7,"line_end":7,"column_start":18,"column_end":75,"is_primary":true,"text":[{"text":"pub use config::{BackoffConfig, CircuitBreakerConfig, DisconnectCallback, FilterConfig, GrpcClientConfig};","highlight_start":18,"highlight_end":75}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `BackoffConfig`, `CircuitBreakerConfig`, and `DisconnectCallback`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/mod.rs:7:18\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use config::{BackoffConfig, CircuitBreakerConfig, DisconnectCallback, FilterConfig, GrpcClientConfig};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `accounts` and `nonempty_txn_signature` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/common/client/config.rs","byte_start":734,"byte_end":746,"line_start":32,"line_end":32,"column_start":12,"column_end":24,"is_primary":false,"text":[{"text":"pub struct FilterConfig {","highlight_start":12,"highlight_end":24}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/common/client/config.rs","byte_start":757,"byte_end":765,"line_start":33,"line_end":33,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    pub accounts: Vec<String>,","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/common/client/config.rs","byte_start":788,"byte_end":810,"line_start":34,"line_end":34,"column_start":9,"column_end":31,"is_primary":true,"text":[{"text":"    pub nonempty_txn_signature: bool,","highlight_start":9,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`FilterConfig` has derived impls for the traits `Debug` and `Clone`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `accounts` and `nonempty_txn_signature` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/config.rs:33:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m32\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct FilterConfig {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub accounts: Vec<String>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub nonempty_txn_signature: bool,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `FilterConfig` has derived impls for the traits `Debug` and `Clone`, but these are intentionally ignored during dead code analysis\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"3 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 3 warnings emitted\u001b[0m\n\n"}
