{"$message_type":"diagnostic","message":"unused imports: `BackoffConfig`, `CircuitBreakerConfig`, and `DisconnectCallback`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/common/client/mod.rs","byte_start":130,"byte_end":143,"line_start":8,"line_end":8,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"pub use config::{BackoffConfig, CircuitBreakerConfig, DisconnectCallback, FilterConfig, GrpcClientConfig};","highlight_start":18,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/common/client/mod.rs","byte_start":145,"byte_end":165,"line_start":8,"line_end":8,"column_start":33,"column_end":53,"is_primary":true,"text":[{"text":"pub use config::{BackoffConfig, CircuitBreakerConfig, DisconnectCallback, FilterConfig, GrpcClientConfig};","highlight_start":33,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/common/client/mod.rs","byte_start":167,"byte_end":185,"line_start":8,"line_end":8,"column_start":55,"column_end":73,"is_primary":true,"text":[{"text":"pub use config::{BackoffConfig, CircuitBreakerConfig, DisconnectCallback, FilterConfig, GrpcClientConfig};","highlight_start":55,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/common/client/mod.rs","byte_start":130,"byte_end":187,"line_start":8,"line_end":8,"column_start":18,"column_end":75,"is_primary":true,"text":[{"text":"pub use config::{BackoffConfig, CircuitBreakerConfig, DisconnectCallback, FilterConfig, GrpcClientConfig};","highlight_start":18,"highlight_end":75}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `BackoffConfig`, `CircuitBreakerConfig`, and `DisconnectCallback`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/mod.rs:8:18\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use config::{BackoffConfig, CircuitBreakerConfig, DisconnectCallback, FilterConfig, GrpcClientConfig};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"1 warning emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 1 warning emitted\u001b[0m\n\n"}
