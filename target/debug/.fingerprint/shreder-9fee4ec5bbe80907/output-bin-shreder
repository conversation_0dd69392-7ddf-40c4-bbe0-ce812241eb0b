{"$message_type":"diagnostic","message":"unused import: `time::Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/common/client/connection.rs","byte_start":32,"byte_end":46,"line_start":1,"line_end":1,"column_start":33,"column_end":47,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, time::Duration};","highlight_start":33,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/common/client/connection.rs","byte_start":30,"byte_end":46,"line_start":1,"line_end":1,"column_start":31,"column_end":47,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, time::Duration};","highlight_start":31,"highlight_end":47}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/common/client/connection.rs","byte_start":9,"byte_end":10,"line_start":1,"line_end":1,"column_start":10,"column_end":11,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, time::Duration};","highlight_start":10,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/common/client/connection.rs","byte_start":46,"byte_end":47,"line_start":1,"line_end":1,"column_start":47,"column_end":48,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, time::Duration};","highlight_start":47,"highlight_end":48}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `time::Duration`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/connection.rs:1:33\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::{collections::HashMap, time::Duration};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `BackoffConfig` and `CircuitBreakerConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/common/client/mod.rs","byte_start":164,"byte_end":177,"line_start":12,"line_end":12,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"pub use config::{BackoffConfig, CircuitBreakerConfig, DisconnectCallback, FilterConfig, GrpcClientConfig};","highlight_start":18,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/common/client/mod.rs","byte_start":179,"byte_end":199,"line_start":12,"line_end":12,"column_start":33,"column_end":53,"is_primary":true,"text":[{"text":"pub use config::{BackoffConfig, CircuitBreakerConfig, DisconnectCallback, FilterConfig, GrpcClientConfig};","highlight_start":33,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/common/client/mod.rs","byte_start":164,"byte_end":201,"line_start":12,"line_end":12,"column_start":18,"column_end":55,"is_primary":true,"text":[{"text":"pub use config::{BackoffConfig, CircuitBreakerConfig, DisconnectCallback, FilterConfig, GrpcClientConfig};","highlight_start":18,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `BackoffConfig` and `CircuitBreakerConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/common/client/mod.rs:12:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use config::{BackoffConfig, CircuitBreakerConfig, DisconnectCallback, FilterConfig, GrpcClientConfig};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"2 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 2 warnings emitted\u001b[0m\n\n"}
