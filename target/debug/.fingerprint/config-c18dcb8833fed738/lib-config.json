{"rustc": 8210029788606052455, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 1398949370558892206, "path": 10162044332273838614, "deps": [[1213098572879462490, "json5_rs", false, 8350828342069620394], [1238778183371849706, "yaml_rust2", false, 1737905453618006593], [2244620803250265856, "ron", false, 17051140854551927373], [2356429411733741858, "ini", false, 13188809833630609034], [6517602928339163454, "path<PERSON><PERSON>", false, 3966760060255182938], [9689903380558560274, "serde", false, 8947787497587030227], [11946729385090170470, "async_trait", false, 7952349356161511727], [13475460906694513802, "convert_case", false, 2135680574445499878], [14718834678227948963, "winnow", false, 3067118353005890949], [15367738274754116744, "serde_json", false, 3579932983937338233], [15609422047640926750, "toml", false, 12803787764421309718]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-c18dcb8833fed738/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}