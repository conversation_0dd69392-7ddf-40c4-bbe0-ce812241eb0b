{"rustc": 8210029788606052455, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17669703692130904899, "path": 17099334823361099506, "deps": [[1615478164327904835, "pin_utils", false, 4081513688587531646], [1906322745568073236, "pin_project_lite", false, 17404025490255456001], [6955678925937229351, "slab", false, 16923665614326569747], [7620660491849607393, "futures_core", false, 16418044051151382260], [10565019901765856648, "futures_macro", false, 15430033174787628588], [16240732885093539806, "futures_task", false, 15876169475522602317]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-5d4c68ab3ee64fad/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}