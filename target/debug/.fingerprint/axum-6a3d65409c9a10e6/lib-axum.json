{"rustc": 8210029788606052455, "features": "[]", "declared_features": "[\"__private\", \"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 4409898591144521313, "path": 17301405828534346599, "deps": [[40386456601120721, "percent_encoding", false, 11759223187841582012], [784494742817713399, "tower_service", false, 5253469082433949734], [1906322745568073236, "pin_project_lite", false, 4770352351195433967], [2517136641825875337, "sync_wrapper", false, 7024699146814501641], [3129130049864710036, "memchr", false, 14063581842545479250], [5695049318159433696, "tower", false, 2047321049603410884], [7695812897323945497, "itoa", false, 361724041893084494], [7712452662827335977, "tower_layer", false, 13445891521177393874], [7858942147296547339, "rustversion", false, 4677344163514874212], [8913795983780778928, "matchit", false, 18188846479067263639], [9010263965687315507, "http", false, 11066688211969040859], [9689903380558560274, "serde", false, 8947787497587030227], [10229185211513642314, "mime", false, 8479029641579145187], [10629569228670356391, "futures_util", false, 1520919064242554074], [14084095096285906100, "http_body", false, 8472964721125619739], [15176407853393882315, "axum_core", false, 14856395912393732995], [16066129441945555748, "bytes", false, 4404919227653090290], [16900715236047033623, "http_body_util", false, 10259577513591973553]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-6a3d65409c9a10e6/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}