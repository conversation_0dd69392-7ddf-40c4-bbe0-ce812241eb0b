{"rustc": 8210029788606052455, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 2734000389832276527, "path": 2382779202385269811, "deps": [[2924422107542798392, "libc", false, 3487678731456865986], [7896293946984509699, "bitflags", false, 11777538378137722921], [12053020504183902936, "build_script_build", false, 15512903694728612533], [14633813869673313769, "libc_errno", false, 10030435626978391165]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-68966b3fe48d938a/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}