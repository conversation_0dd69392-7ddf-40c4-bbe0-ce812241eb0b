/Users/<USER>/PJ/shreder/target/debug/deps/shreder-46e91f4442ac4563.d: /Users/<USER>/PJ/shreder/clippy.toml src/main.rs src/common/mod.rs src/common/client/mod.rs src/common/client/backoff.rs src/common/client/config.rs src/common/client/connection.rs src/common/client/grpc_client.rs src/common/client/state.rs src/config/mod.rs src/config/app.rs src/config/logger.rs src/core/mod.rs src/core/config.rs src/core/logger.rs src/generated.rs src/generated/shredstream.rs Cargo.toml

/Users/<USER>/PJ/shreder/target/debug/deps/libshreder-46e91f4442ac4563.rmeta: /Users/<USER>/PJ/shreder/clippy.toml src/main.rs src/common/mod.rs src/common/client/mod.rs src/common/client/backoff.rs src/common/client/config.rs src/common/client/connection.rs src/common/client/grpc_client.rs src/common/client/state.rs src/config/mod.rs src/config/app.rs src/config/logger.rs src/core/mod.rs src/core/config.rs src/core/logger.rs src/generated.rs src/generated/shredstream.rs Cargo.toml

/Users/<USER>/PJ/shreder/clippy.toml:
src/main.rs:
src/common/mod.rs:
src/common/client/mod.rs:
src/common/client/backoff.rs:
src/common/client/config.rs:
src/common/client/connection.rs:
src/common/client/grpc_client.rs:
src/common/client/state.rs:
src/config/mod.rs:
src/config/app.rs:
src/config/logger.rs:
src/core/mod.rs:
src/core/config.rs:
src/core/logger.rs:
src/generated.rs:
src/generated/shredstream.rs:
Cargo.toml:

# env-dep:CARGO_PKG_NAME=shreder
# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
