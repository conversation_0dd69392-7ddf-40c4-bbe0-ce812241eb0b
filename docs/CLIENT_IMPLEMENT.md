# GRPC Client Implementation Specification

## Overview

This document provides complete specification for implementing the GRPC client that connects to Jito Shredstream endpoints. The client handles connection management, subscription, reconnection with advanced features like exponential backoff, circuit breaker, and idle timeout detection.

## Core Requirements

### Connection Success Criteria
- Connection is considered successful ONLY when BOTH connect AND subscribe operations succeed
- This applies to ALL connections (initial connection and reconnections)
- Any failure in either connect or subscribe triggers the retry cycle

### Subscription Management
- Subscribe with account filters and nonempty_txn_signature flag
- Subscribe failure automatically closes connection, performs cleanup, and restarts from beginning
- Must validate subscription success before considering connection established

### Reconnection System
- Auto-reconnect with exponential backoff and jitter
- Reset retry count only when BOTH connect + subscribe succeed
- Handle partial failures (connect success but subscribe failure)

## Configuration Structure

### GrpcClientConfig
```rust
#[derive(Debug, Clone)]
pub struct GrpcClientConfig {
    pub endpoint: String,                 // GRPC endpoint URL
    pub connect_timeout: Duration,        // Default: 10s
    pub idle_timeout: Duration,           // Default: 30s  
    pub auto_reconnect: bool,             // Default: true
    pub backoff: BackoffConfig,
    pub circuit_breaker: CircuitBreakerConfig,
    pub filters: FilterConfig,
}
```

### BackoffConfig
```rust
#[derive(Debug, Clone)]
pub struct BackoffConfig {
    pub initial_delay: Duration,          // Default: 1s
    pub max_delay: Duration,              // Default: 60s
    pub multiplier: f64,                  // Default: 2.0
    pub jitter_percent: f64,              // Default: 0.1 (10% jitter)
    pub max_retries: Option<u32>,         // Default: Some(10)
    pub reset_on_success: bool,           // Default: true
}
```

### CircuitBreakerConfig
```rust
#[derive(Debug, Clone)]
pub struct CircuitBreakerConfig {
    pub enabled: bool,                    // Default: true
    pub failure_threshold: u32,           // Default: 2
    pub time_window: Duration,            // Default: 60s
}
```

### FilterConfig
```rust
#[derive(Debug, Clone)]
pub struct FilterConfig {
    pub accounts: Vec<String>,            // Default: empty
    pub nonempty_txn_signature: bool,     // Default: true
}
```

### Callback Type
```rust
pub type DisconnectCallback = Box<dyn Fn(String, Option<Box<dyn std::error::Error>>) + Send + Sync>;
```

## Feature Specifications

### 1. Connection Management

#### Initial Connection
- Connect to GRPC endpoint with configurable timeout
- Throw exception on connect timeout
- Validate connection by attempting subscription

#### Connection Validation
- Connection success = Connect success + Subscribe success
- Partial success (connect OK, subscribe failed) = Connection failure
- Must cleanup and restart on any failure

#### Connection Cleanup
- Properly close GRPC connection
- Clear internal state
- Reset connection-related resources

### 2. Subscription System

#### Filter Implementation
- Use SubscribeRequestFilterAccounts with:
  - `account`: List of account addresses to monitor
  - `nonempty_txn_signature`: Boolean flag for transaction signature filtering
- No commitment level filtering
- No other filter types (owner, memcmp, etc.)

#### Subscription Validation
- Subscribe request must complete successfully
- Handle subscription errors gracefully
- Auto-close connection on subscribe failure

#### Subscribe Failure Handling
- Immediately close GRPC connection
- Perform complete cleanup
- Restart entire connection cycle from beginning
- Log subscription failure with details

### 3. Exponential Backoff System

#### Backoff Calculation
```rust
// Pseudocode for backoff calculation
let base_delay = initial_delay * (multiplier ^ retry_count);
let capped_delay = min(base_delay, max_delay);
let jitter_amount = capped_delay * jitter_percent * random(0.0, 1.0);
let final_delay = capped_delay + jitter_amount;
```

#### Jitter Implementation
- Apply random jitter to prevent thundering herd
- Jitter percentage configurable (default 10%)
- Add jitter to calculated delay, not subtract

#### Retry Count Management
- Increment on each failure (connect OR subscribe)
- Reset to 0 on successful connect + subscribe
- Check max_retries before each attempt
- Stop retrying when max_retries exceeded

### 4. Circuit Breaker (Time-based)

#### Failure Tracking
- Track timestamp of each failure
- Maintain sliding window of failures within time_window
- Count consecutive failures within the time window

#### Circuit Breaker Logic
```rust
// Pseudocode for circuit breaker check
let recent_failures = failures.iter()
    .filter(|&timestamp| now - timestamp <= time_window)
    .count();

if recent_failures >= failure_threshold {
    // Circuit breaker triggered
    call_disconnect_callback("Circuit breaker triggered", None);
    close_client_permanently();
}
```

#### Circuit Breaker Actions
- When triggered: Stop all retry attempts
- Call disconnect callback with reason
- Close client permanently (no recovery)
- Log circuit breaker activation

### 5. Idle Timeout Detection

#### Last Message Tracking
- Record timestamp of each received Entry message
- Update timestamp on successful message reception
- Monitor time since last message

#### Idle Timeout Logic
```rust
// Pseudocode for idle detection
if now - last_message_time > idle_timeout {
    log_idle_timeout();
    close_connection();
    if auto_reconnect {
        start_reconnection_cycle();
    }
}
```

#### Idle Timeout Actions
- Close current connection gracefully
- Trigger reconnection cycle if auto_reconnect enabled
- Log idle timeout event with details

### 6. Error Handling

#### Error Categories
1. **Connect Timeout**: Throw exception immediately
2. **Connect Failure**: Network/connection errors → Retry cycle
3. **Subscribe Failure**: Subscription errors → Close + Retry cycle
4. **Stream Errors**: Runtime stream errors → Close + Retry cycle
5. **Idle Timeout**: No messages received → Close + Retry cycle

#### Exception Handling
- Connect timeout: Throw exception to caller
- Other errors: Handle internally with retry logic
- Permanent failures: Call disconnect callback

#### Callback Interface
```rust
// Callback signature
fn disconnect_callback(reason: String, error: Option<Box<dyn std::error::Error>>) {
    // Application-level decision:
    // - Shutdown entire application
    // - Disable this client and continue
    // - Log and monitor
}
```

### 7. State Machine

#### States
1. **Disconnected**: Initial state, no connection
2. **Connecting**: Attempting GRPC connection
3. **Subscribing**: Connection established, attempting subscription
4. **Connected**: Both connect and subscribe successful
5. **Retrying**: In backoff delay before next attempt
6. **CircuitOpen**: Circuit breaker triggered, permanently closed

#### State Transitions
```
Disconnected → Connecting → Subscribing → Connected
     ↑              ↓           ↓           ↓
     ←─── Retrying ←─────────────────────────
     ↓
CircuitOpen (permanent)
```

### 8. Logging Requirements

#### Log Events
- Connection attempts (start, success, failure)
- Subscription attempts (start, success, failure)
- Retry attempts with backoff delays
- Circuit breaker state changes
- Idle timeout detections
- Message reception (periodic stats)
- Error events with full context

#### Log Levels
- **INFO**: Normal operations (connect, subscribe, disconnect)
- **WARN**: Retries, timeouts, recoverable errors
- **ERROR**: Permanent failures, circuit breaker triggers
- **DEBUG**: Detailed state changes, message counts

#### Log Format
- Include timestamp, client ID, endpoint
- Structured logging with relevant context
- Error details with stack traces when applicable

## Implementation Guidelines

### Thread Safety
- Client must be thread-safe for concurrent access
- Use appropriate synchronization primitives
- Handle async operations safely

### Resource Management
- Proper cleanup of GRPC connections
- Memory management for message buffers
- Graceful shutdown procedures

### Performance Considerations
- Minimize allocation in hot paths
- Efficient message handling
- Non-blocking operations where possible

### Testing Strategy
- Unit tests for each component
- Integration tests with mock GRPC server
- Error injection testing
- Load testing for performance validation

## Usage Example

```rust
let config = GrpcClientConfig {
    endpoint: "https://shredstream.jito.wtf".to_string(),
    filters: FilterConfig {
        accounts: vec!["account1".to_string(), "account2".to_string()],
        nonempty_txn_signature: true,
    },
    ..Default::default()
};

let disconnect_callback = Box::new(|reason, error| {
    eprintln!("Client disconnected: {}", reason);
    if let Some(err) = error {
        eprintln!("Error: {:?}", err);
    }
    // Application decision: shutdown or continue
});

let mut client = GrpcClient::new(config, disconnect_callback);
let stream = client.connect_and_subscribe().await?;

// Process entries from stream
while let Some(entry) = stream.next().await {
    // Handle entry
}
```

## Error Recovery Scenarios

### Scenario 1: Network Interruption
1. Stream error detected
2. Close connection and cleanup
3. Start retry cycle with backoff
4. Attempt connect + subscribe
5. Resume normal operation on success

### Scenario 2: Subscribe Failure
1. Connect succeeds, subscribe fails
2. Immediately close connection
3. Increment retry count
4. Apply backoff delay
5. Restart from connect phase

### Scenario 3: Circuit Breaker Trigger
1. Multiple failures within time window
2. Circuit breaker activates
3. Call disconnect callback
4. Stop all retry attempts
5. Client permanently closed

### Scenario 4: Idle Timeout
1. No messages received for idle_timeout duration
2. Close connection gracefully
3. Start reconnection cycle (if enabled)
4. Resume normal operation on successful reconnect

This specification provides complete implementation guidance for the GRPC client with all required features and error handling scenarios.
