mod common;
mod config;
mod core;
mod generated;

use core::{config::load_config, logger::Logger};
use std::time::Duration;

use common::client::{FilterConfig, GrpcClient, GrpcClientConfig};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    let config = load_config()?;
    let _logger = Logger::init(&config.logger)?;

    tracing::info!("Starting Shreder application");

    let grpc_config = GrpcClientConfig {
        endpoint: "https://shredstream.jito.wtf".to_string(),
        connect_timeout: Duration::from_secs(10),
        filters: FilterConfig {
            accounts: vec!["11111111111111111111111111111112".to_string()],
            nonempty_txn_signature: true,
        },
        ..Default::default()
    };

    let disconnect_callback = Box::new(|reason: String, error: Option<anyhow::Error>| {
        tracing::error!(
            reason = %reason,
            has_error = error.is_some(),
            "GRPC client disconnected permanently"
        );
        if let Some(err) = error {
            tracing::error!(error = %err, "Disconnect error details");
        }
    });

    let mut client = GrpcClient::new(grpc_config, Some(disconnect_callback));

    tracing::info!("Attempting to connect to GRPC endpoint");

    match client.connect_and_subscribe().await {
        Ok(mut stream) => {
            tracing::info!("Successfully connected and subscribed to entries stream");

            use futures_util::StreamExt;

            let mut message_count = 0;
            while let Some(result) = stream.next().await {
                match result {
                    Ok(entry) => {
                        message_count += 1;
                        client.update_last_message_time();

                        tracing::info!(
                            slot = entry.slot,
                            entries_size = entry.entries.len(),
                            message_count = message_count,
                            "Received entry from stream"
                        );

                        if client.check_idle_timeout() {
                            tracing::warn!("Idle timeout detected, should reconnect");
                            break;
                        }

                        if message_count >= 10 {
                            tracing::info!("Received 10 messages, stopping for demo");
                            break;
                        }
                    }
                    Err(e) => {
                        tracing::error!(error = %e, "Error receiving entry from stream");
                        break;
                    }
                }
            }

            client.shutdown().await?;
        }
        Err(e) => {
            tracing::error!(error = %e, "Failed to connect to GRPC endpoint");
        }
    }

    tracing::info!("Shreder application finished");
    Ok(())
}
