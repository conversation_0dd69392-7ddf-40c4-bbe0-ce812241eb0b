use serde::{Deserialize, Serialize};
use validator::<PERSON>ida<PERSON>;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, Valida<PERSON>)]
pub struct LoggerConfig {
    #[validate(custom(function = "validate_log_level"))]
    pub level: String,

    pub format: LogFormat,

    #[serde(default)]
    pub async_logging: bool,

    #[serde(default)]
    pub target_filtering: bool,

    #[serde(default)]
    pub module_filters: Option<String>,

    #[serde(default)]
    pub show_target: bool,

    #[serde(default)]
    pub show_thread_ids: bool,

    #[serde(default)]
    pub compact_format: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
#[serde(rename_all = "lowercase")]
pub enum LogFormat {
    Json,
    #[default]
    Pretty,
}

impl Default for LoggerConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            format: LogFormat::Pretty,
            async_logging: true,
            target_filtering: true,
            module_filters: None,
            show_target: false,
            show_thread_ids: false,
            compact_format: true,
        }
    }
}

fn validate_log_level(level: &str) -> Result<(), validator::ValidationError> {
    match level.to_lowercase().as_str() {
        "trace" | "debug" | "info" | "warn" | "error" | "off" => Ok(()),
        _ => {
            let mut error = validator::ValidationError::new("invalid_log_level");
            error.message = Some("Log level must be one of: trace, debug, info, warn, error, off".into());
            Err(error)
        }
    }
}
