use std::time::{Duration, Instant};

use super::config::BackoffConfig;

pub struct BackoffCalculator;

impl BackoffCalculator {
    pub fn calculate_delay(config: &BackoffConfig, retry_count: u32, client_ptr: *const ()) -> Duration {
        let base_delay = config.initial_delay.as_millis() as f64 * config.multiplier.powi(retry_count as i32);
        let capped_delay = Duration::from_millis((base_delay as u64).min(config.max_delay.as_millis() as u64));

        if config.jitter_percent > 0.0 {
            Self::apply_jitter(capped_delay, config.jitter_percent, client_ptr)
        } else {
            capped_delay
        }
    }

    fn apply_jitter(delay: Duration, jitter_percent: f64, client_ptr: *const ()) -> Duration {
        use std::{
            collections::hash_map::DefaultHasher,
            hash::{Hash, Has<PERSON>},
        };

        let mut hasher = DefaultHasher::new();

        client_ptr.hash(&mut hasher);
        Instant::now().elapsed().as_nanos().hash(&mut hasher);

        let seed = hasher.finish();
        let random_factor = (seed % 1000) as f64 / 1000.0;
        let jitter_amount = delay.as_millis() as f64 * jitter_percent * random_factor;

        Duration::from_millis(delay.as_millis() as u64 + jitter_amount as u64)
    }
}
