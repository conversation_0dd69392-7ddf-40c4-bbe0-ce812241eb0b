pub mod backoff;
pub mod config;
pub mod connection;
pub mod state;

use std::{
    sync::{Arc, Mutex},
    time::Instant,
};

use anyhow::Result;
pub use config::{BackoffConfig, CircuitBreakerConfig, DisconnectCallback, FilterConfig, GrpcClientConfig};
use state::{CircuitBreakerState, ClientState};
use tokio::time::sleep;
use tonic::{codec::Streaming, transport::Channel};
use tracing::{debug, error, info, warn};

use self::{backoff::BackoffCalculator, connection::ConnectionManager};
use crate::generated::{Entry, shredstream_proxy_client::ShredstreamProxyClient};

pub struct GrpcClient {
    config: GrpcClientConfig,
    state: Arc<Mutex<ClientState>>,
    client: Option<ShredstreamProxyClient<Channel>>,
    retry_count: u32,
    circuit_breaker: CircuitBreakerState,
    last_message_time: Option<Instant>,
    disconnect_callback: Option<DisconnectCallback>,
}

impl GrpcClient {
    pub fn new(config: GrpcClientConfig, disconnect_callback: Option<DisconnectCallback>) -> Self {
        info!(endpoint = %config.endpoint, "Creating new GRPC client");

        Self {
            config,
            state: Arc::new(Mutex::new(ClientState::Disconnected)),
            client: None,
            retry_count: 0,
            circuit_breaker: CircuitBreakerState::new(),
            last_message_time: None,
            disconnect_callback,
        }
    }

    pub async fn connect_and_subscribe(&mut self) -> Result<Streaming<Entry>> {
        info!(endpoint = %self.config.endpoint, "Starting connect and subscribe process");

        loop {
            if self.should_trigger_circuit_breaker() {
                self.trigger_circuit_breaker("Circuit breaker activated due to consecutive failures");
                return Err(anyhow::anyhow!("Circuit breaker triggered"));
            }

            if let Some(max_retries) = self.config.backoff.max_retries {
                if self.retry_count >= max_retries {
                    self.call_disconnect_callback(
                        "Max retries exceeded",
                        Some(anyhow::anyhow!("Exceeded {} retries", max_retries)),
                    );
                    return Err(anyhow::anyhow!("Max retries exceeded"));
                }
            }

            match self.attempt_connect_and_subscribe().await {
                Ok(stream) => {
                    info!(
                        endpoint = %self.config.endpoint,
                        retry_count = self.retry_count,
                        "Successfully connected and subscribed"
                    );

                    if self.config.backoff.reset_on_success {
                        self.retry_count = 0;
                        self.circuit_breaker.clear();
                        debug!("Reset retry count and circuit breaker on successful connection");
                    }

                    self.set_state(ClientState::Connected);
                    self.last_message_time = Some(Instant::now());
                    return Ok(stream);
                }
                Err(e) => {
                    error!(
                        endpoint = %self.config.endpoint,
                        retry_count = self.retry_count,
                        error = %e,
                        "Failed to connect and subscribe"
                    );

                    self.record_failure();
                    self.retry_count += 1;

                    if !self.config.auto_reconnect {
                        self.call_disconnect_callback("Connection failed and auto-reconnect disabled", Some(e));
                        return Err(anyhow::anyhow!("Connection failed"));
                    }

                    self.set_state(ClientState::Retrying);
                    let delay = self.calculate_backoff_delay();
                    warn!(
                        endpoint = %self.config.endpoint,
                        retry_count = self.retry_count,
                        delay_ms = delay.as_millis(),
                        "Retrying connection after delay"
                    );

                    sleep(delay).await;
                }
            }
        }
    }

    async fn attempt_connect_and_subscribe(&mut self) -> Result<Streaming<Entry>> {
        self.set_state(ClientState::Connecting);

        let (client, stream) = ConnectionManager::connect_and_subscribe(&self.config, &self.config.endpoint).await?;

        self.client = Some(client);
        self.set_state(ClientState::Subscribing);

        Ok(stream)
    }

    pub fn update_last_message_time(&mut self) {
        self.last_message_time = Some(Instant::now());
    }

    pub fn check_idle_timeout(&self) -> bool {
        if let Some(last_time) = self.last_message_time {
            Instant::now().duration_since(last_time) > self.config.idle_timeout
        } else {
            false
        }
    }

    pub async fn shutdown(&mut self) -> Result<()> {
        info!(endpoint = %self.config.endpoint, "Shutting down GRPC client");

        self.set_state(ClientState::Disconnected);
        self.client = None;
        self.last_message_time = None;

        debug!(endpoint = %self.config.endpoint, "GRPC client shutdown completed");

        Ok(())
    }

    fn set_state(&self, new_state: ClientState) {
        if let Ok(mut state) = self.state.lock() {
            debug!(
                endpoint = %self.config.endpoint,
                old_state = ?*state,
                new_state = ?new_state,
                "Client state transition"
            );
            *state = new_state;
        }
    }

    fn should_trigger_circuit_breaker(&self) -> bool {
        if !self.config.circuit_breaker.enabled {
            return false;
        }

        self.circuit_breaker.should_trigger(self.config.circuit_breaker.failure_threshold)
    }

    fn trigger_circuit_breaker(&mut self, reason: &str) {
        error!(
            endpoint = %self.config.endpoint,
            reason = reason,
            failure_count = self.circuit_breaker.failure_count(),
            "Circuit breaker triggered"
        );

        self.set_state(ClientState::CircuitOpen);
        self.call_disconnect_callback(reason, None);
    }

    fn record_failure(&mut self) {
        if self.config.circuit_breaker.enabled {
            let now = Instant::now();
            self.circuit_breaker.record_failure(now, self.config.circuit_breaker.time_window);

            debug!(
                endpoint = %self.config.endpoint,
                failure_count = self.circuit_breaker.failure_count(),
                time_window_secs = self.config.circuit_breaker.time_window.as_secs(),
                "Recorded failure for circuit breaker"
            );
        }
    }

    fn calculate_backoff_delay(&self) -> std::time::Duration {
        BackoffCalculator::calculate_delay(
            &self.config.backoff,
            self.retry_count,
            std::ptr::addr_of!(*self) as *const (),
        )
    }

    fn call_disconnect_callback(&self, reason: &str, error: Option<anyhow::Error>) {
        if let Some(ref callback) = self.disconnect_callback {
            warn!(
                endpoint = %self.config.endpoint,
                reason = reason,
                has_error = error.is_some(),
                "Calling disconnect callback"
            );

            callback(reason.to_string(), error);
        }
    }
}
