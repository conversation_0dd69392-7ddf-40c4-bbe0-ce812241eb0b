use std::time::{Duration, Instant};

#[derive(Debug)]
pub enum ClientState {
    Disconnected,
    Connecting,
    Subscribing,
    Connected,
    Retrying,
    CircuitOpen,
}

#[derive(Debug)]
pub struct CircuitBreakerState {
    failure_times: Vec<Instant>,
}

impl CircuitBreakerState {
    pub fn new() -> Self {
        Self { failure_times: Vec::new() }
    }

    pub fn record_failure(&mut self, now: Instant, time_window: Duration) {
        self.failure_times.push(now);
        self.failure_times.retain(|&time| now.duration_since(time) <= time_window);
    }

    pub fn should_trigger(&self, threshold: u32) -> bool {
        self.failure_times.len() >= threshold as usize
    }

    pub fn clear(&mut self) {
        self.failure_times.clear();
    }

    pub fn failure_count(&self) -> usize {
        self.failure_times.len()
    }
}
