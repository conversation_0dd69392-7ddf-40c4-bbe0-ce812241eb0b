use std::time::Duration;

#[derive(Debug, Clone)]
pub struct GrpcClientConfig {
    pub endpoint: String,
    pub connect_timeout: Duration,
    pub idle_timeout: Duration,
    pub auto_reconnect: bool,
    pub backoff: BackoffConfig,
    pub circuit_breaker: CircuitBreakerConfig,
    pub filters: FilterConfig,
}

#[derive(Debug, Clone)]
pub struct BackoffConfig {
    pub initial_delay: Duration,
    pub max_delay: Duration,
    pub multiplier: f64,
    pub jitter_percent: f64,
    pub max_retries: Option<u32>,
    pub reset_on_success: bool,
}

#[derive(Debug, Clone)]
pub struct CircuitBreakerConfig {
    pub enabled: bool,
    pub failure_threshold: u32,
    pub time_window: Duration,
}

#[derive(Debug, Clone)]
pub struct FilterConfig {
    pub accounts: Vec<String>,
    pub nonempty_txn_signature: bool,
}

pub type DisconnectCallback = Box<dyn Fn(String, Option<anyhow::Error>) + Send + Sync>;

impl Default for GrpcClientConfig {
    fn default() -> Self {
        Self {
            endpoint: String::new(),
            connect_timeout: Duration::from_secs(10),
            idle_timeout: Duration::from_secs(30),
            auto_reconnect: true,
            backoff: BackoffConfig::default(),
            circuit_breaker: CircuitBreakerConfig::default(),
            filters: FilterConfig::default(),
        }
    }
}

impl Default for BackoffConfig {
    fn default() -> Self {
        Self {
            initial_delay: Duration::from_secs(1),
            max_delay: Duration::from_secs(60),
            multiplier: 2.0,
            jitter_percent: 0.1,
            max_retries: Some(10),
            reset_on_success: true,
        }
    }
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self { enabled: true, failure_threshold: 2, time_window: Duration::from_secs(60) }
    }
}

impl Default for FilterConfig {
    fn default() -> Self {
        Self { accounts: Vec::new(), nonempty_txn_signature: true }
    }
}
