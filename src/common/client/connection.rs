use std::collections::HashMap;

use anyhow::{Context, Result};
use tokio::time::timeout;
use tonic::{
    Request,
    codec::Streaming,
    transport::{Channel, Endpoint},
};
use tracing::{debug, info};

use super::config::{FilterConfig, GrpcClientConfig};
use crate::generated::{
    Entry, SubscribeEntriesRequest, SubscribeRequestFilterAccounts, shredstream_proxy_client::ShredstreamProxyClient,
};

pub struct ConnectionManager;

impl ConnectionManager {
    pub async fn connect_and_subscribe(
        config: &GrpcClientConfig,
        endpoint: &str,
    ) -> Result<(ShredstreamProxyClient<Channel>, Streaming<Entry>)> {
        debug!(endpoint = %endpoint, "Attempting to connect to GRPC endpoint");

        let endpoint = Endpoint::from_shared(endpoint.to_string()).context("Invalid endpoint URL")?;

        let channel = timeout(config.connect_timeout, endpoint.connect())
            .await
            .context("Connection timeout")?
            .context("Failed to establish connection")?;

        let mut client = ShredstreamProxyClient::new(channel);
        debug!(endpoint = %config.endpoint, "GRPC connection established, attempting subscription");

        let request = Self::build_subscribe_request(&config.filters);
        info!(
            endpoint = %config.endpoint,
            accounts_count = request.accounts.len(),
            transactions_count = request.transactions.len(),
            slots_count = request.slots.len(),
            commitment = ?request.commitment,
            "Built subscribe request"
        );

        if let Some(accounts_filter) = request.accounts.get("default") {
            info!(
                endpoint = %config.endpoint,
                account_filters = ?accounts_filter.account,
                owner_filters = ?accounts_filter.owner,
                nonempty_txn_signature = ?accounts_filter.nonempty_txn_signature,
                "Account filter details"
            );
        }

        let stream = timeout(config.connect_timeout, client.subscribe_entries(Request::new(request)))
            .await
            .context("Subscribe timeout")?
            .map_err(|e| {
                debug!(endpoint = %config.endpoint, error = %e, "Subscribe error details");
                anyhow::anyhow!("Failed to subscribe to entries: {}", e)
            })?
            .into_inner();

        debug!(endpoint = %config.endpoint, "Successfully subscribed to entries stream");

        Ok((client, stream))
    }

    fn build_subscribe_request(filters: &FilterConfig) -> SubscribeEntriesRequest {
        let mut accounts_map = HashMap::new();

        let filter = SubscribeRequestFilterAccounts {
            account: filters.accounts.clone(),
            owner: Vec::new(),
            filters: Vec::new(),
            nonempty_txn_signature: Some(filters.nonempty_txn_signature),
        };

        accounts_map.insert("default".to_string(), filter);

        SubscribeEntriesRequest {
            accounts: accounts_map,
            transactions: HashMap::new(),
            slots: HashMap::new(),
            commitment: None,
        }
    }
}
