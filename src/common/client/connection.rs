use std::collections::HashMap;

use anyhow::{Context, Result};
use tokio::time::timeout;
use tonic::{
    Request,
    codec::Streaming,
    transport::{Channel, Endpoint},
};
use tracing::debug;

use super::config::{FilterConfig, GrpcClientConfig};
use crate::generated::{
    Entry, SubscribeEntriesRequest, SubscribeRequestFilterAccounts, shredstream_proxy_client::ShredstreamProxyClient,
};

pub struct ConnectionManager;

impl ConnectionManager {
    pub async fn connect_and_subscribe(
        config: &GrpcClientConfig,
        endpoint: &str,
    ) -> Result<(ShredstreamProxyClient<Channel>, Streaming<Entry>)> {
        debug!(endpoint = %endpoint, "Attempting to connect to GRPC endpoint");

        let endpoint = Endpoint::from_shared(endpoint.to_string()).context("Invalid endpoint URL")?;

        let channel = timeout(config.connect_timeout, endpoint.connect())
            .await
            .context("Connection timeout")?
            .context("Failed to establish connection")?;

        let mut client = ShredstreamProxyClient::new(channel);
        debug!(endpoint = %config.endpoint, "GRPC connection established, attempting subscription");

        let request = Self::build_subscribe_request(&config.filters);
        let stream = timeout(config.connect_timeout, client.subscribe_entries(Request::new(request)))
            .await
            .context("Subscribe timeout")?
            .context("Failed to subscribe to entries")?
            .into_inner();

        debug!(endpoint = %config.endpoint, "Successfully subscribed to entries stream");

        Ok((client, stream))
    }

    fn build_subscribe_request(filters: &FilterConfig) -> SubscribeEntriesRequest {
        let mut accounts_map = HashMap::new();

        if !filters.accounts.is_empty() || filters.nonempty_txn_signature {
            let filter = SubscribeRequestFilterAccounts {
                account: filters.accounts.clone(),
                owner: Vec::new(),
                filters: Vec::new(),
                nonempty_txn_signature: Some(filters.nonempty_txn_signature),
            };

            accounts_map.insert("default".to_string(), filter);
        }

        SubscribeEntriesRequest {
            accounts: accounts_map,
            transactions: HashMap::new(),
            slots: HashMap::new(),
            commitment: None,
        }
    }
}
