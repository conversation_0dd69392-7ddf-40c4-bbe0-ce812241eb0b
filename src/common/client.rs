use std::{
    collections::HashMap,
    sync::{Arc, Mutex},
    time::{Duration, Instant},
};

use anyhow::{Context, Result};
use tokio::time::{sleep, timeout};
use tonic::{
    Request,
    codec::Streaming,
    transport::{Channel, Endpoint},
};
use tracing::{debug, error, info, warn};

use crate::generated::shredstream::{
    Entry, SubscribeEntriesRequest, SubscribeRequestFilterAccounts, shredstream_proxy_client::ShredstreamProxyClient,
};

#[derive(Debug, Clone)]
pub struct GrpcClientConfig {
    pub endpoint: String,
    pub connect_timeout: Duration,
    pub idle_timeout: Duration,
    pub auto_reconnect: bool,
    pub backoff: BackoffConfig,
    pub circuit_breaker: CircuitBreakerConfig,
    pub filters: FilterConfig,
}

#[derive(Debug, Clone)]
pub struct BackoffConfig {
    pub initial_delay: Duration,
    pub max_delay: Duration,
    pub multiplier: f64,
    pub jitter_percent: f64,
    pub max_retries: Option<u32>,
    pub reset_on_success: bool,
}

#[derive(Debug, <PERSON>lone)]
pub struct CircuitBreakerConfig {
    pub enabled: bool,
    pub failure_threshold: u32,
    pub time_window: Duration,
}

#[derive(Debug, Clone)]
pub struct FilterConfig {
    pub accounts: Vec<String>,
    pub nonempty_txn_signature: bool,
}

pub type DisconnectCallback = Box<dyn Fn(String, Option<anyhow::Error>) + Send + Sync>;

impl Default for GrpcClientConfig {
    fn default() -> Self {
        Self {
            endpoint: String::new(),
            connect_timeout: Duration::from_secs(10),
            idle_timeout: Duration::from_secs(30),
            auto_reconnect: true,
            backoff: BackoffConfig::default(),
            circuit_breaker: CircuitBreakerConfig::default(),
            filters: FilterConfig::default(),
        }
    }
}

impl Default for BackoffConfig {
    fn default() -> Self {
        Self {
            initial_delay: Duration::from_secs(1),
            max_delay: Duration::from_secs(60),
            multiplier: 2.0,
            jitter_percent: 0.1,
            max_retries: Some(10),
            reset_on_success: bool::default(),
        }
    }
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self { enabled: true, failure_threshold: 2, time_window: Duration::from_secs(60) }
    }
}

impl Default for FilterConfig {
    fn default() -> Self {
        Self { accounts: Vec::new(), nonempty_txn_signature: true }
    }
}

#[derive(Debug)]
enum ClientState {
    Disconnected,
    Connecting,
    Subscribing,
    Connected,
    Retrying,
    CircuitOpen,
}

#[derive(Debug)]
struct CircuitBreakerState {
    failure_times: Vec<Instant>,
}

impl CircuitBreakerState {
    fn new() -> Self {
        Self { failure_times: Vec::new() }
    }

    fn record_failure(&mut self, now: Instant, time_window: Duration) {
        self.failure_times.push(now);
        self.failure_times.retain(|&time| now.duration_since(time) <= time_window);
    }

    fn should_trigger(&self, threshold: u32) -> bool {
        self.failure_times.len() >= threshold as usize
    }

    fn clear(&mut self) {
        self.failure_times.clear();
    }
}

pub struct GrpcClient {
    config: GrpcClientConfig,
    state: Arc<Mutex<ClientState>>,
    client: Option<ShredstreamProxyClient<Channel>>,
    retry_count: u32,
    circuit_breaker: CircuitBreakerState,
    last_message_time: Option<Instant>,
    disconnect_callback: Option<DisconnectCallback>,
}

impl GrpcClient {
    pub fn new(config: GrpcClientConfig, disconnect_callback: Option<DisconnectCallback>) -> Self {
        info!(
            endpoint = %config.endpoint,
            "Creating new GRPC client"
        );

        Self {
            config,
            state: Arc::new(Mutex::new(ClientState::Disconnected)),
            client: None,
            retry_count: 0,
            circuit_breaker: CircuitBreakerState::new(),
            last_message_time: None,
            disconnect_callback,
        }
    }

    pub async fn connect_and_subscribe(&mut self) -> Result<Streaming<Entry>> {
        info!(
            endpoint = %self.config.endpoint,
            "Starting connect and subscribe process"
        );

        loop {
            if self.should_trigger_circuit_breaker() {
                self.trigger_circuit_breaker("Circuit breaker activated due to consecutive failures");
                return Err(anyhow::anyhow!("Circuit breaker triggered"));
            }

            if let Some(max_retries) = self.config.backoff.max_retries {
                if self.retry_count >= max_retries {
                    self.call_disconnect_callback(
                        "Max retries exceeded",
                        Some(anyhow::anyhow!("Exceeded {} retries", max_retries)),
                    );
                    return Err(anyhow::anyhow!("Max retries exceeded"));
                }
            }

            match self.attempt_connect_and_subscribe().await {
                Ok(stream) => {
                    info!(
                        endpoint = %self.config.endpoint,
                        retry_count = self.retry_count,
                        "Successfully connected and subscribed"
                    );

                    if self.config.backoff.reset_on_success {
                        self.retry_count = 0;
                        self.circuit_breaker.clear();
                        debug!("Reset retry count and circuit breaker on successful connection");
                    }

                    self.set_state(ClientState::Connected);
                    self.last_message_time = Some(Instant::now());
                    return Ok(stream);
                }
                Err(e) => {
                    error!(
                        endpoint = %self.config.endpoint,
                        retry_count = self.retry_count,
                        error = %e,
                        "Failed to connect and subscribe"
                    );

                    self.record_failure();
                    self.retry_count += 1;

                    if !self.config.auto_reconnect {
                        self.call_disconnect_callback("Connection failed and auto-reconnect disabled", Some(e));
                        return Err(anyhow::anyhow!("Connection failed"));
                    }

                    self.set_state(ClientState::Retrying);
                    let delay = self.calculate_backoff_delay();
                    warn!(
                        endpoint = %self.config.endpoint,
                        retry_count = self.retry_count,
                        delay_ms = delay.as_millis(),
                        "Retrying connection after delay"
                    );

                    sleep(delay).await;
                }
            }
        }
    }

    async fn attempt_connect_and_subscribe(&mut self) -> Result<Streaming<Entry>> {
        self.set_state(ClientState::Connecting);
        debug!(
            endpoint = %self.config.endpoint,
            "Attempting to connect to GRPC endpoint"
        );

        let endpoint = Endpoint::from_shared(self.config.endpoint.clone()).context("Invalid endpoint URL")?;

        let channel = timeout(self.config.connect_timeout, endpoint.connect())
            .await
            .context("Connection timeout")?
            .context("Failed to establish connection")?;

        let mut client = ShredstreamProxyClient::new(channel);
        debug!(
            endpoint = %self.config.endpoint,
            "GRPC connection established, attempting subscription"
        );

        self.set_state(ClientState::Subscribing);

        let request = self.build_subscribe_request();
        let stream = timeout(self.config.connect_timeout, client.subscribe_entries(Request::new(request)))
            .await
            .context("Subscribe timeout")?
            .context("Failed to subscribe to entries")?
            .into_inner();

        self.client = Some(client);
        debug!(
            endpoint = %self.config.endpoint,
            "Successfully subscribed to entries stream"
        );

        Ok(stream)
    }

    fn build_subscribe_request(&self) -> SubscribeEntriesRequest {
        let mut accounts_map = HashMap::new();

        if !self.config.filters.accounts.is_empty() || self.config.filters.nonempty_txn_signature {
            let filter = SubscribeRequestFilterAccounts {
                account: self.config.filters.accounts.clone(),
                owner: Vec::new(),
                filters: Vec::new(),
                nonempty_txn_signature: Some(self.config.filters.nonempty_txn_signature),
            };

            accounts_map.insert("default".to_string(), filter);
        }

        SubscribeEntriesRequest {
            accounts: accounts_map,
            transactions: HashMap::new(),
            slots: HashMap::new(),
            commitment: None,
        }
    }

    pub fn update_last_message_time(&mut self) {
        self.last_message_time = Some(Instant::now());
    }

    pub fn check_idle_timeout(&self) -> bool {
        if let Some(last_time) = self.last_message_time {
            Instant::now().duration_since(last_time) > self.config.idle_timeout
        } else {
            false
        }
    }

    pub async fn shutdown(&mut self) -> Result<()> {
        info!(
            endpoint = %self.config.endpoint,
            "Shutting down GRPC client"
        );

        self.set_state(ClientState::Disconnected);
        self.client = None;
        self.last_message_time = None;

        debug!(
            endpoint = %self.config.endpoint,
            "GRPC client shutdown completed"
        );

        Ok(())
    }

    fn set_state(&self, new_state: ClientState) {
        if let Ok(mut state) = self.state.lock() {
            debug!(
                endpoint = %self.config.endpoint,
                old_state = ?*state,
                new_state = ?new_state,
                "Client state transition"
            );
            *state = new_state;
        }
    }

    fn should_trigger_circuit_breaker(&self) -> bool {
        if !self.config.circuit_breaker.enabled {
            return false;
        }

        self.circuit_breaker.should_trigger(self.config.circuit_breaker.failure_threshold)
    }

    fn trigger_circuit_breaker(&mut self, reason: &str) {
        error!(
            endpoint = %self.config.endpoint,
            reason = reason,
            failure_count = self.circuit_breaker.failure_times.len(),
            "Circuit breaker triggered"
        );

        self.set_state(ClientState::CircuitOpen);
        self.call_disconnect_callback(reason, None);
    }

    fn record_failure(&mut self) {
        if self.config.circuit_breaker.enabled {
            let now = Instant::now();
            self.circuit_breaker.record_failure(now, self.config.circuit_breaker.time_window);

            debug!(
                endpoint = %self.config.endpoint,
                failure_count = self.circuit_breaker.failure_times.len(),
                time_window_secs = self.config.circuit_breaker.time_window.as_secs(),
                "Recorded failure for circuit breaker"
            );
        }
    }

    fn calculate_backoff_delay(&self) -> Duration {
        let base_delay = self.config.backoff.initial_delay.as_millis() as f64
            * self.config.backoff.multiplier.powi(self.retry_count as i32);

        let capped_delay =
            Duration::from_millis((base_delay as u64).min(self.config.backoff.max_delay.as_millis() as u64));

        if self.config.backoff.jitter_percent > 0.0 {
            use std::{
                collections::hash_map::DefaultHasher,
                hash::{Hash, Hasher},
            };

            let mut hasher = DefaultHasher::new();
            std::ptr::addr_of!(self).hash(&mut hasher);
            Instant::now().elapsed().as_nanos().hash(&mut hasher);
            let seed = hasher.finish();

            let random_factor = (seed % 1000) as f64 / 1000.0;
            let jitter_amount = capped_delay.as_millis() as f64 * self.config.backoff.jitter_percent * random_factor;

            Duration::from_millis(capped_delay.as_millis() as u64 + jitter_amount as u64)
        } else {
            capped_delay
        }
    }

    fn call_disconnect_callback(&self, reason: &str, error: Option<anyhow::Error>) {
        if let Some(ref callback) = self.disconnect_callback {
            warn!(
                endpoint = %self.config.endpoint,
                reason = reason,
                has_error = error.is_some(),
                "Calling disconnect callback"
            );

            callback(reason.to_string(), error);
        }
    }
}
