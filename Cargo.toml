[package]
name = "shreder"
version = "0.1.0"
edition = "2024"

[[bin]]
name = "protogen"
path = "protogen.rs"

[dependencies]
anyhow = "1.0.98"
config = "0.15.11"
futures-util = "0.3.31"
prost = "0.13.5"
prost-build = "0.13.5"
serde = { version = "1.0.219", features = ["derive"] }
tokio = { version = "1.45.1", features = ["rt-multi-thread"] }
tonic = "0.13.1"
tonic-build = "0.13.1"
tracing = "0.1.41"
tracing-appender = "0.2.3"
tracing-subscriber = { version = "0.3.19", features = ["json", "env-filter"] }
validator = { version = "0.20.0", features = ["derive"] }

[build-dependencies]
prost-build = "0.13.5"
tonic-build = "0.13.1"
